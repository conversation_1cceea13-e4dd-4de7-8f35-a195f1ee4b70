#!/usr/bin/env python3
"""
Fix OpenCV installation for License Plate Monitoring System
"""
import subprocess
import sys

def fix_opencv():
    print("🔧 Fixing OpenCV installation...")
    
    try:
        # Step 1: Uninstall all OpenCV packages
        print("📦 Removing existing OpenCV packages...")
        packages_to_remove = [
            "opencv-python", 
            "opencv-contrib-python", 
            "opencv-python-headless",
            "opencv-contrib-python-headless"
        ]
        
        for package in packages_to_remove:
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "uninstall", package, "-y"
                ], capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ Removed {package}")
                else:
                    print(f"ℹ️  {package} not installed")
            except Exception as e:
                print(f"⚠️  Error removing {package}: {e}")
        
        # Step 2: Install the correct OpenCV version
        print("\n📦 Installing opencv-python==********...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "opencv-python==********"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ OpenCV installed successfully!")
        else:
            print(f"❌ Failed to install OpenCV: {result.stderr}")
            return False
        
        # Step 3: Test the installation
        print("\n🧪 Testing OpenCV installation...")
        try:
            import cv2
            print(f"✅ OpenCV version: {cv2.__version__}")
            
            if hasattr(cv2, 'VideoCapture'):
                print("✅ VideoCapture available")
                
                # Test creating VideoCapture
                cap = cv2.VideoCapture(0)
                print("✅ VideoCapture object created successfully")
                cap.release()
                
                print("🎉 OpenCV is working correctly!")
                return True
            else:
                print("❌ VideoCapture not available in cv2 module")
                return False
                
        except ImportError as e:
            print(f"❌ Failed to import OpenCV after installation: {e}")
            return False
        except Exception as e:
            print(f"❌ Error testing OpenCV: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚗 License Plate Monitoring System - OpenCV Fix")
    print("=" * 50)
    
    success = fix_opencv()
    
    if success:
        print("\n✅ OpenCV fix completed successfully!")
        print("🚀 You can now run: python main.py")
    else:
        print("\n❌ OpenCV fix failed!")
        print("💡 Try manually running:")
        print("   pip uninstall opencv-python opencv-contrib-python -y")
        print("   pip install opencv-python==********")
