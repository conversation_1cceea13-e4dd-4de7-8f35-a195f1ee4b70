#!/usr/bin/env python3
"""
Comprehensive fix script for license plate recognition system issues
"""

import os
import sys
import json
import subprocess

def clear_environment_variables():
    """Clear potentially conflicting environment variables"""
    env_vars = [
        'GEMINI_API_KEY',
        'GOOGLE_API_KEY', 
        'GOOGLE_GENAI_USE_VERTEXAI',
        'GOOGLE_CLOUD_PROJECT',
        'GOOGLE_CLOUD_LOCATION'
    ]
    
    print("🧹 Clearing environment variables...")
    cleared = []
    for var in env_vars:
        if os.getenv(var):
            os.environ.pop(var, None)
            cleared.append(var)
    
    if cleared:
        print(f"   Cleared: {', '.join(cleared)}")
    else:
        print("   No environment variables to clear")

def validate_config():
    """Validate config.json"""
    print("📋 Validating config.json...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check API key
        api_key = config.get('ai', {}).get('gemini', {}).get('api_key', '')
        if not api_key:
            print("❌ No API key in config.json")
            return False
        elif api_key == "YOUR_GEMINI_API_KEY_HERE":
            print("❌ Placeholder API key in config.json")
            return False
        elif not (api_key.startswith('AIza') and len(api_key) == 39):
            print(f"⚠️ API key format unusual: {api_key[:8]}... (length: {len(api_key)})")
        else:
            print(f"✅ API key looks valid: {api_key[:8]}...{api_key[-4:]}")
        
        # Check model
        model = config.get('ai', {}).get('gemini', {}).get('model', '')
        print(f"🤖 Model: {model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
        return False

def test_api_connection():
    """Test API connection"""
    print("🧪 Testing API connection...")
    
    try:
        from ai_processor import AIVisionProcessor
        processor = AIVisionProcessor()
        
        if processor.test_api_connection():
            print("✅ API connection successful!")
            return True
        else:
            print("❌ API connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def run_dependency_check():
    """Run dependency check"""
    print("🔍 Checking dependencies...")
    try:
        subprocess.run([sys.executable, "check_dependencies.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Dependency check failed")
        return False
    except FileNotFoundError:
        print("⚠️ check_dependencies.py not found, skipping...")
        return True

def main():
    """Main fix routine"""
    print("🔧 Comprehensive License Plate Recognition System Fix")
    print("=" * 60)
    
    # Step 1: Check dependencies
    print("\n📦 Step 1: Checking Dependencies")
    run_dependency_check()
    
    # Step 2: Clear environment variables
    print("\n🌍 Step 2: Environment Variables")
    clear_environment_variables()
    
    # Step 3: Validate configuration
    print("\n⚙️ Step 3: Configuration Validation")
    config_valid = validate_config()
    
    if not config_valid:
        print("❌ Configuration validation failed. Please fix config.json")
        return
    
    # Step 4: Test API connection
    print("\n🔌 Step 4: API Connection Test")
    api_works = test_api_connection()
    
    # Summary
    print("\n📊 Summary")
    print("=" * 30)
    
    if api_works:
        print("✅ System appears to be working correctly!")
        print("🚀 You can now run: python launcher.py --web --detection-mode object_detection")
    else:
        print("❌ Issues remain. Please check:")
        print("   1. API key is valid and not expired")
        print("   2. Internet connection is working")
        print("   3. Gemini API service is available")
        print("   4. No firewall blocking the connection")
        
        print("\n💡 Troubleshooting steps:")
        print("   1. Verify API key at: https://aistudio.google.com/app/apikey")
        print("   2. Check API quotas and billing")
        print("   3. Try a different model name in config.json")
        print("   4. Run: python debug_api_key.py for detailed diagnostics")

if __name__ == "__main__":
    main()
