#!/usr/bin/env python3
"""
Test script for RTSP camera connection
"""
import sys
import time
from camera_handler import RTSPCameraHandler

def test_camera():
    """Test the RTSP camera handler"""
    print("🚗 Testing RTSP Camera Connection")
    print("=" * 50)
    
    try:
        # Initialize camera handler
        print("📹 Initializing RTSP camera handler...")
        camera = RTSPCameraHandler("config.json")
        print("✅ Camera handler initialized")
        
        # Test connection
        print("\n🔄 Testing camera connection...")
        if camera.connect():
            print("✅ Camera connected successfully!")
            
            # Test capturing a few frames
            print("\n📸 Testing frame capture...")
            for i in range(3):
                print(f"Capturing frame {i+1}...")
                result = camera.capture_image()
                
                if result:
                    filepath, frame = result
                    print(f"✅ Frame {i+1} captured: {filepath}")
                    print(f"   Frame shape: {frame.shape if frame is not None else 'None'}")
                else:
                    print(f"❌ Failed to capture frame {i+1}")
                
                time.sleep(1)
            
            # Disconnect
            print("\n🔌 Disconnecting camera...")
            camera.disconnect()
            print("✅ Camera disconnected")
            
            return True
            
        else:
            print("❌ Failed to connect to camera")
            return False
            
    except Exception as e:
        print(f"❌ Error during camera test: {e}")
        return False

def test_camera_with_context_manager():
    """Test camera using context manager"""
    print("\n🔄 Testing camera with context manager...")
    
    try:
        with RTSPCameraHandler("config.json") as camera:
            if camera.is_connected:
                print("✅ Camera connected via context manager")
                
                # Capture one frame
                result = camera.capture_image()
                if result:
                    filepath, frame = result
                    print(f"✅ Frame captured via context manager: {filepath}")
                    return True
                else:
                    print("❌ Failed to capture frame via context manager")
                    return False
            else:
                print("❌ Camera not connected via context manager")
                return False
                
    except Exception as e:
        print(f"❌ Error with context manager test: {e}")
        return False

if __name__ == "__main__":
    print("🚗 License Plate Monitoring System - Camera Test")
    print("=" * 60)
    
    # Test 1: Basic connection and capture
    success1 = test_camera()
    
    # Test 2: Context manager
    success2 = test_camera_with_context_manager()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"Basic camera test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Context manager test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 All camera tests passed! RTSP camera is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some camera tests failed. Check the configuration and network connectivity.")
        sys.exit(1)
