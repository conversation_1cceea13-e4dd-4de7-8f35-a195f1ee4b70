#!/usr/bin/env python3
"""
Web Application for License Plate Recognition System
Provides web interface for manual capture and auto mode control
"""

import os
import json
import threading
import time
import webbrowser
from datetime import datetime
from typing import Optional, Dict, Any
from flask import Flask, render_template, jsonify, request, send_file
from flask_cors import CORS

# Import existing system components
from main import AIVisionMonitor
from config_manager import get_config_manager
from logger import get_logger

app = Flask(__name__)
CORS(app)

class WebLicensePlateSystem:
    def __init__(self):
        """Initialize the web-based license plate system"""
        self.config_manager = get_config_manager()
        self.logger = get_logger()
        self.monitor = None
        self.auto_mode_thread = None
        self.auto_mode_running = False
        self.current_mode = "web"
        self.last_capture_result = None
        self.system_status = "ready"
        
        # Initialize the monitor
        self._initialize_monitor()
        
    def _initialize_monitor(self):
        """Initialize the AI vision monitor with fallback to mock camera"""
        try:
            # First try with real camera
            self.monitor = AIVisionMonitor(use_mock_camera=False)
            self.logger.info("✅ AI Vision Monitor initialized for web interface")
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to initialize with real camera: {str(e)}")
            try:
                # Fallback to mock camera
                self.logger.info("🔄 Falling back to mock camera...")
                self.monitor = AIVisionMonitor(use_mock_camera=True)
                self.logger.info("✅ AI Vision Monitor initialized with mock camera")
            except Exception as e2:
                self.logger.error(f"❌ Failed to initialize even with mock camera: {str(e2)}")
                self.system_status = "error"
    
    def manual_capture(self) -> Dict[str, Any]:
        """Perform a manual capture and return results"""
        if not self.monitor:
            return {
                'success': False,
                'error': 'System not initialized',
                'timestamp': datetime.now().isoformat()
            }

        try:
            self.system_status = "capturing"
            self.logger.info("📸 Manual capture initiated from web interface")

            # Perform capture and processing - now returns AI results
            capture_result = self.monitor.capture_and_process()

            if capture_result and capture_result.get('success'):
                # Extract AI results for web display
                ai_result = capture_result.get('ai_result', {})

                result = {
                    'success': True,
                    'timestamp': capture_result.get('timestamp', datetime.now().isoformat()),
                    'image_path': capture_result.get('image_path'),
                    'mode': capture_result.get('mode', 'manual'),
                    'ai_analysis': self._format_ai_results(ai_result, capture_result.get('mode'))
                }

                self.last_capture_result = result
                self.system_status = "ready"

                return result
            else:
                error_msg = capture_result.get('error', 'Unknown capture error') if capture_result else 'No result returned'
                self.logger.error(f"❌ Capture failed: {error_msg}")
                self.system_status = "error"
                return {
                    'success': False,
                    'error': error_msg,
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            self.logger.error(f"❌ Manual capture failed: {str(e)}")
            self.system_status = "error"
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def start_auto_mode(self, interval_seconds: int) -> Dict[str, Any]:
        """Start auto mode with specified interval"""
        if self.auto_mode_running:
            self.stop_auto_mode()
        
        try:
            self.current_mode = "auto"
            self.auto_mode_running = True
            
            # Update config with new interval
            config = self.config_manager.get_config()
            config['operation']['mode'] = 'auto'
            config['operation']['auto_mode']['interval_seconds'] = interval_seconds
            config['operation']['auto_mode']['enabled'] = True
            config['operation']['web_mode']['enabled'] = False
            
            # Start auto mode thread
            self.auto_mode_thread = threading.Thread(
                target=self._auto_mode_worker,
                args=(interval_seconds,),
                daemon=True
            )
            self.auto_mode_thread.start()
            
            self.logger.info(f"🔄 Auto mode started with {interval_seconds}s interval")
            
            return {
                'success': True,
                'mode': 'auto',
                'interval_seconds': interval_seconds,
                'message': f'Auto mode started with {interval_seconds} second interval'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start auto mode: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_auto_mode(self) -> Dict[str, Any]:
        """Stop auto mode and return to web mode"""
        try:
            self.auto_mode_running = False
            self.current_mode = "web"
            
            # Update config
            config = self.config_manager.get_config()
            config['operation']['mode'] = 'web'
            config['operation']['auto_mode']['enabled'] = False
            config['operation']['web_mode']['enabled'] = True
            
            if self.auto_mode_thread and self.auto_mode_thread.is_alive():
                # Wait for thread to finish
                self.auto_mode_thread.join(timeout=2)
            
            self.logger.info("🔄 Auto mode stopped, switched to web mode")
            
            return {
                'success': True,
                'mode': 'web',
                'message': 'Switched to web mode'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to stop auto mode: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _auto_mode_worker(self, interval_seconds: int):
        """Worker thread for auto mode captures"""
        self.logger.info(f"🤖 Auto mode worker started (interval: {interval_seconds}s)")

        while self.auto_mode_running:
            try:
                self.system_status = "auto_capturing"
                capture_result = self.monitor.capture_and_process()

                if capture_result and capture_result.get('success'):
                    # Extract AI results for web display
                    ai_result = capture_result.get('ai_result', {})

                    self.last_capture_result = {
                        'success': True,
                        'timestamp': capture_result.get('timestamp', datetime.now().isoformat()),
                        'image_path': capture_result.get('image_path'),
                        'mode': 'auto',
                        'ai_analysis': self._format_ai_results(ai_result, capture_result.get('mode'))
                    }
                else:
                    error_msg = capture_result.get('error', 'Unknown capture error') if capture_result else 'No result returned'
                    self.last_capture_result = {
                        'success': False,
                        'error': error_msg,
                        'timestamp': datetime.now().isoformat(),
                        'mode': 'auto'
                    }

                self.system_status = "auto_waiting"

                # Wait for the specified interval
                for _ in range(interval_seconds):
                    if not self.auto_mode_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"❌ Auto mode capture failed: {str(e)}")
                self.system_status = "error"
                time.sleep(5)  # Wait before retrying

        self.logger.info("🔄 Auto mode worker stopped")
        self.system_status = "ready"
    
    def _format_ai_results(self, ai_result: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """Format AI results for web display"""
        if not ai_result or not ai_result.get('success'):
            return {
                'type': 'error',
                'message': ai_result.get('error', 'AI processing failed') if ai_result else 'No AI results available'
            }

        if mode == 'license_plate':
            plates = ai_result.get('license_plates', [])
            plates_detected = ai_result.get('plates_detected', 0)

            if plates_detected == 0:
                return {
                    'type': 'license_plate',
                    'plates_detected': 0,
                    'message': 'No license plates detected in the image',
                    'raw_response': ai_result.get('raw_response', '')
                }

            formatted_plates = []
            for i, plate in enumerate(plates, 1):
                formatted_plates.append({
                    'number': i,
                    'plate_number': plate.get('plate_number', 'Unknown'),
                    'location': plate.get('location', 'Unknown location'),
                    'confidence': plate.get('confidence', 'Unknown')
                })

            return {
                'type': 'license_plate',
                'plates_detected': plates_detected,
                'plates': formatted_plates,
                'raw_response': ai_result.get('raw_response', '')
            }

        elif mode == 'object_detection':
            return {
                'type': 'object_detection',
                'description': ai_result.get('description', 'No description available'),
                'word_count': ai_result.get('word_count', 0),
                'raw_response': ai_result.get('raw_response', '')
            }

        return {
            'type': 'unknown',
            'message': f'Unknown mode: {mode}',
            'raw_response': ai_result.get('raw_response', '')
        }

    def _get_latest_image(self) -> Optional[str]:
        """Get the path to the latest captured image"""
        try:
            storage_config = self.config_manager.get_storage_config()
            images_dir = storage_config.get('images_directory', 'captured_images')

            if not os.path.exists(images_dir):
                return None

            # Get all image files
            image_files = [f for f in os.listdir(images_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

            if not image_files:
                return None

            # Sort by modification time and get the latest
            image_files.sort(key=lambda x: os.path.getmtime(os.path.join(images_dir, x)), reverse=True)
            latest_image = os.path.join(images_dir, image_files[0])

            return latest_image

        except Exception as e:
            self.logger.error(f"❌ Failed to get latest image: {str(e)}")
            return None
    
    def set_detection_mode(self, mode: str) -> Dict[str, Any]:
        """Set the detection mode (license_plate or object_detection)"""
        try:
            available_modes = self.config_manager.get_available_modes()
            if mode not in available_modes:
                return {
                    'success': False,
                    'error': f'Invalid mode. Available modes: {available_modes}'
                }

            # Update the system mode
            self.config_manager.set_mode(mode)

            # Reinitialize monitor with new mode
            if self.monitor:
                self.monitor.current_mode = mode
                self.monitor.mode_config = self.config_manager.get_mode_config(mode)

            self.logger.info(f"🔄 Detection mode changed to: {mode}")

            return {
                'success': True,
                'mode': mode,
                'mode_name': self.config_manager.get_mode_config(mode).get('name', mode)
            }

        except Exception as e:
            self.logger.error(f"❌ Failed to set detection mode: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        current_detection_mode = self.config_manager.get_current_mode()
        mode_config = self.config_manager.get_mode_config(current_detection_mode)

        return {
            'mode': self.current_mode,  # web/auto mode
            'status': self.system_status,
            'auto_mode_running': self.auto_mode_running,
            'last_capture': self.last_capture_result,
            'detection_mode': current_detection_mode,  # license_plate/object_detection
            'detection_mode_name': mode_config.get('name', current_detection_mode),
            'available_modes': self.config_manager.get_available_modes(),
            'timestamp': datetime.now().isoformat()
        }

# Initialize the system
web_system = WebLicensePlateSystem()

# Flask Routes
@app.route('/')
def index():
    """Main web interface"""
    return render_template('index.html')

@app.route('/api/capture', methods=['POST'])
def api_capture():
    """Manual capture endpoint"""
    result = web_system.manual_capture()
    return jsonify(result)

@app.route('/api/start_auto', methods=['POST'])
def api_start_auto():
    """Start auto mode endpoint"""
    data = request.get_json()
    interval_seconds = data.get('interval_seconds', 60)
    
    # Validate interval
    if not isinstance(interval_seconds, int) or interval_seconds < 1:
        return jsonify({
            'success': False,
            'error': 'Invalid interval. Must be a positive integer.'
        }), 400
    
    result = web_system.start_auto_mode(interval_seconds)
    return jsonify(result)

@app.route('/api/stop_auto', methods=['POST'])
def api_stop_auto():
    """Stop auto mode endpoint"""
    result = web_system.stop_auto_mode()
    return jsonify(result)

@app.route('/api/status', methods=['GET'])
def api_status():
    """Get system status endpoint"""
    status = web_system.get_status()
    return jsonify(status)

@app.route('/api/set_detection_mode', methods=['POST'])
def api_set_detection_mode():
    """Set detection mode endpoint"""
    data = request.get_json()
    mode = data.get('mode')

    if not mode:
        return jsonify({
            'success': False,
            'error': 'Mode parameter is required'
        }), 400

    result = web_system.set_detection_mode(mode)
    return jsonify(result)

@app.route('/api/image/<path:filename>')
def api_image(filename):
    """Serve captured images"""
    try:
        storage_config = web_system.config_manager.get_storage_config()
        images_dir = storage_config.get('images_directory', 'captured_images')
        image_path = os.path.join(images_dir, filename)

        if os.path.exists(image_path):
            response = send_file(image_path)
            # Add cache-busting headers for fresh images
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response
        else:
            return jsonify({'error': 'Image not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/latest_image')
def api_latest_image():
    """Get the latest captured image"""
    try:
        latest_image = web_system._get_latest_image()
        if latest_image:
            response = send_file(latest_image)
            # Add cache-busting headers to prevent browser caching
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
            return response
        else:
            return jsonify({'error': 'No images found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_web_server():
    """Run the Flask web server"""
    config = get_config_manager().get_config()
    web_config = config.get('web_server', {})
    
    host = web_config.get('host', '0.0.0.0')
    port = web_config.get('port', 8080)
    debug = web_config.get('debug', False)
    auto_open = web_config.get('auto_open_browser', True)
    
    # Open browser automatically
    if auto_open:
        threading.Timer(1.5, lambda: webbrowser.open(f'http://localhost:{port}')).start()
    
    print(f"🌐 Starting License Plate Recognition Web Interface...")
    print(f"🔗 Access at: http://localhost:{port}")
    print(f"📱 Or from other devices: http://{host}:{port}")
    print(f"🔄 Mode: Web Interface with Auto Mode Support")
    
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    run_web_server()
