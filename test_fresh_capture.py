#!/usr/bin/env python3
"""
Test script to verify fresh capture functionality
This script tests the camera capture to ensure each capture produces a unique image
"""

import os
import sys
import time
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from camera_handler import CameraHandler
from config_manager import get_config_manager

def test_fresh_captures(num_captures=3):
    """Test multiple captures to ensure each one is fresh"""
    print("🧪 Testing Fresh Capture Functionality")
    print("=" * 50)
    
    # Initialize camera handler
    config_manager = get_config_manager()
    camera = CameraHandler(config_manager)
    
    captured_files = []
    
    try:
        # Connect to camera
        print("📷 Connecting to camera...")
        if not camera.connect():
            print("❌ Failed to connect to camera")
            return False
        
        print("✅ Camera connected successfully")
        
        # Perform multiple captures
        for i in range(num_captures):
            print(f"\n📸 Capture {i+1}/{num_captures}")
            print("-" * 30)
            
            # Wait a moment between captures
            if i > 0:
                print("⏳ Waiting 2 seconds before next capture...")
                time.sleep(2)
            
            # Test regular capture
            print("🔄 Testing regular capture...")
            result = camera.capture_image()
            
            if result:
                image_path, frame = result
                captured_files.append(image_path)
                file_size = os.path.getsize(image_path)
                print(f"✅ Regular capture successful: {os.path.basename(image_path)} ({file_size} bytes)")
            else:
                print("❌ Regular capture failed")
                continue
            
            # Wait a moment
            time.sleep(1)
            
            # Test fresh capture
            print("🔄 Testing fresh capture...")
            fresh_result = camera.force_fresh_capture()
            
            if fresh_result:
                fresh_path, fresh_frame = fresh_result
                captured_files.append(fresh_path)
                fresh_size = os.path.getsize(fresh_path)
                print(f"✅ Fresh capture successful: {os.path.basename(fresh_path)} ({fresh_size} bytes)")
            else:
                print("❌ Fresh capture failed")
        
        # Analyze results
        print(f"\n📊 Analysis Results")
        print("=" * 50)
        print(f"Total captures: {len(captured_files)}")
        
        if len(captured_files) >= 2:
            # Check file sizes
            sizes = [os.path.getsize(f) for f in captured_files]
            unique_sizes = len(set(sizes))
            print(f"Unique file sizes: {unique_sizes}/{len(captured_files)}")
            
            # Check timestamps in filenames
            timestamps = [os.path.basename(f).split('_')[1:3] for f in captured_files]
            unique_timestamps = len(set([tuple(t) for t in timestamps]))
            print(f"Unique timestamps: {unique_timestamps}/{len(captured_files)}")
            
            # List all captured files
            print("\n📁 Captured Files:")
            for i, file_path in enumerate(captured_files, 1):
                size = os.path.getsize(file_path)
                filename = os.path.basename(file_path)
                print(f"  {i}. {filename} ({size} bytes)")
            
            if unique_timestamps == len(captured_files):
                print("\n✅ SUCCESS: All captures have unique timestamps!")
                return True
            else:
                print("\n⚠️  WARNING: Some captures may have duplicate timestamps")
                return False
        else:
            print("❌ Not enough captures to analyze")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False
    
    finally:
        # Clean up
        camera.disconnect()
        print("\n🔌 Camera disconnected")

def main():
    """Main test function"""
    print(f"🚀 Fresh Capture Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_fresh_captures(num_captures=3)
    
    if success:
        print("\n🎉 All tests passed! Fresh capture is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
