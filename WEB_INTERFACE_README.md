# License Plate Recognition Web Interface

## Overview

The License Plate Recognition system now includes a modern web interface that provides both manual capture and automated monitoring capabilities. The system supports two operational modes:

- **Web Mode**: Manual capture with instant processing
- **Auto Mode**: Automated capture at configurable intervals

## Features

### 🌐 Web Interface
- Modern, responsive design
- Real-time status updates
- Live image display
- AI analysis results
- Mode switching capabilities

### 📸 Web Mode (Manual)
- Click-to-capture functionality
- Instant image processing
- Immediate AI analysis results
- Perfect for on-demand monitoring

### ⏰ Auto Mode (Scheduled)
- Configurable capture intervals (in seconds)
- Automatic image processing
- Continuous monitoring
- Easy start/stop controls

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure the System
Edit `config.json` to set your camera URL and API key:
```json
{
    "camera": {
        "rtsp": {
            "url": "rtsp://admin:admin@*************:1935"
        }
    },
    "ai": {
        "gemini": {
            "api_key": "your-gemini-api-key"
        }
    }
}
```

### 3. Start the Web Interface
```bash
python launcher.py --web
```

The web interface will automatically open in your browser at `http://localhost:8080`

## Usage Guide

### Web Mode (Manual Capture)
1. Ensure the system is in "Web Mode" (default)
2. Click the "Capture & Analyze Now" button
3. Wait for the image to be captured and processed
4. View results in the AI Analysis section

### Auto Mode (Scheduled Capture)
1. Set your desired interval in seconds (minimum 1 second)
2. Click "Start Auto Mode"
3. The system will automatically capture and process images
4. Click "Stop Auto Mode" to return to web mode

### Configuration Options

#### Capture Intervals
- **Minimum**: 1 second
- **Maximum**: 3600 seconds (1 hour)
- **Recommended**: 30-60 seconds for continuous monitoring

#### Operation Modes
The system automatically switches between:
- **Web Mode**: Manual control, on-demand capture
- **Auto Mode**: Scheduled capture at specified intervals

## Configuration Reference

### New Configuration Sections

#### Operation Settings
```json
"operation": {
    "mode": "web",
    "auto_mode": {
        "interval_seconds": 60,
        "enabled": false
    },
    "web_mode": {
        "enabled": true
    }
}
```

#### Web Server Settings
```json
"web_server": {
    "host": "0.0.0.0",
    "port": 8080,
    "debug": false,
    "auto_open_browser": true
}
```

#### Updated Capture Settings
```json
"capture": {
    "interval_seconds": 60,
    "save_images": true,
    "image_quality": 95,
    "auto_reconnect": true
}
```

## API Endpoints

The web interface provides RESTful API endpoints:

- `GET /` - Main web interface
- `POST /api/capture` - Manual capture
- `POST /api/start_auto` - Start auto mode
- `POST /api/stop_auto` - Stop auto mode
- `GET /api/status` - System status
- `GET /api/latest_image` - Latest captured image
- `GET /api/image/<filename>` - Specific image file

## Launcher Options

The new launcher script provides multiple startup modes:

```bash
# Start web interface (default)
python launcher.py --web

# Start traditional monitoring
python launcher.py --monitor

# Run system test
python launcher.py --test

# Run demo mode
python launcher.py --demo

# Use mock camera
python launcher.py --web --mock-camera
```

## Troubleshooting

### Common Issues

#### Flask Not Found
```bash
pip install Flask Flask-CORS
```

#### Port Already in Use
Change the port in `config.json`:
```json
"web_server": {
    "port": 5001
}
```

#### Camera Connection Issues
1. Check your RTSP URL in `config.json`
2. Use mock camera for testing: `python launcher.py --web --mock-camera`
3. Verify network connectivity to camera

#### AI Processing Errors
1. Verify your Gemini API key in `config.json`
2. Check internet connectivity
3. Review logs in `logs/ai_vision_monitor.log`

### Browser Compatibility
- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

### Network Access
- Local access: `http://localhost:8080`
- Network access: `http://your-ip-address:8080`
- Configure firewall if needed

## File Structure

```
Python/Number Plate/
├── launcher.py              # Main launcher script
├── web_app.py              # Flask web application
├── templates/
│   └── index.html          # Web interface
├── config.json             # System configuration
├── main.py                 # Traditional monitoring
├── camera_handler.py       # Camera management
├── ai_processor.py         # AI processing
└── requirements.txt        # Dependencies
```

## Security Considerations

- The web server binds to `0.0.0.0` by default for network access
- Consider using a reverse proxy (nginx) for production
- API endpoints have basic validation but no authentication
- Images are served directly from the filesystem

## Performance Tips

- Use appropriate capture intervals to balance monitoring and system load
- Monitor disk space for captured images
- Consider image cleanup policies in production
- Use mock camera for development and testing

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review system logs
3. Test with mock camera
4. Verify configuration settings
