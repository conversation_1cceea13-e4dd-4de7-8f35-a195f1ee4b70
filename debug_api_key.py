#!/usr/bin/env python3
"""
Debug script to check API key configuration and test Gemini API connection
"""

import os
import json
from config_manager import get_config_manager
from logger import get_logger

def main():
    """Debug API key configuration"""
    logger = get_logger()
    config_manager = get_config_manager()
    
    print("🔍 API Key Configuration Debug")
    print("=" * 50)
    
    # Check config.json
    try:
        with open('config.json', 'r') as f:
            config_data = json.load(f)
        
        config_api_key = config_data.get('ai', {}).get('gemini', {}).get('api_key', '')
        print(f"📄 Config.json API key: {config_api_key[:8]}...{config_api_key[-4:] if len(config_api_key) > 12 else '***'}")
        print(f"📄 Config.json API key length: {len(config_api_key)}")
        print(f"📄 Config.json model: {config_data.get('ai', {}).get('gemini', {}).get('model', 'Not set')}")
        
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
    
    # Check environment variables
    env_gemini_key = os.getenv('GEMINI_API_KEY', '')
    env_google_key = os.getenv('GOOGLE_API_KEY', '')
    
    print(f"\n🌍 Environment Variables:")
    print(f"GEMINI_API_KEY: {'Set' if env_gemini_key else 'Not set'}")
    if env_gemini_key:
        print(f"  Value: {env_gemini_key[:8]}...{env_gemini_key[-4:] if len(env_gemini_key) > 12 else '***'}")
        print(f"  Length: {len(env_gemini_key)}")
    
    print(f"GOOGLE_API_KEY: {'Set' if env_google_key else 'Not set'}")
    if env_google_key:
        print(f"  Value: {env_google_key[:8]}...{env_google_key[-4:] if len(env_google_key) > 12 else '***'}")
        print(f"  Length: {len(env_google_key)}")
    
    # Check what the config manager returns
    ai_config = config_manager.get_ai_config()
    gemini_config = ai_config.get('gemini', {})
    
    print(f"\n⚙️ Config Manager Results:")
    print(f"AI Config: {ai_config}")
    print(f"Gemini Config: {gemini_config}")
    
    # Test API key priority logic (same as in ai_processor.py)
    config_api_key = gemini_config.get('api_key', '').strip()
    env_api_key = os.getenv('GEMINI_API_KEY', '').strip()
    
    if config_api_key and config_api_key != "YOUR_GEMINI_API_KEY_HERE":
        final_api_key = config_api_key
        key_source = "config.json"
    elif env_api_key:
        final_api_key = env_api_key
        key_source = "GEMINI_API_KEY environment variable"
    else:
        final_api_key = ""
        key_source = "none"
    
    print(f"\n🎯 Final API Key Selection:")
    print(f"Source: {key_source}")
    if final_api_key:
        print(f"Key: {final_api_key[:8]}...{final_api_key[-4:] if len(final_api_key) > 12 else '***'}")
        print(f"Length: {len(final_api_key)}")
        print(f"Valid format: {final_api_key.startswith('AIza') and len(final_api_key) == 39}")
    else:
        print("Key: None")
    
    # Test the AI processor
    print(f"\n🤖 Testing AI Processor:")
    try:
        from ai_processor import AIVisionProcessor
        processor = AIVisionProcessor()
        
        if processor.test_api_connection():
            print("✅ AI Processor connection successful!")
        else:
            print("❌ AI Processor connection failed!")
            
    except Exception as e:
        print(f"❌ Error testing AI processor: {e}")

if __name__ == "__main__":
    main()
